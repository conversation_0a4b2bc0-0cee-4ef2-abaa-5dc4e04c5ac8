# ESP8266 + K230 实时视频监控系统

## 系统概述
本系统通过ESP8266创建WebSocket服务器，接收K230发送的JPEG视频流，并在网页上实时显示。

## 硬件要求
- ESP8266开发板
- K230开发板
- 路由器（可选，用于外网访问）

## 依赖库
- ESPAsyncTCP
- ESPAsyncWebServer

## 网络架构
```
K230 ──WiFi──> ESP8266(AP) ──WiFi──> 路由器 ──> 用户设备
     (客户端)    (热点+客户端)   (网关)    (浏览器)
```

## 配置说明

### 1. ESP8266配置
编辑 `config.h` 文件：
```cpp
#define STA_SSID "your_router_ssid"        // 修改为您的路由器SSID
#define STA_PASSWORD "your_router_password" // 修改为您的路由器密码
```

### 2. K230配置
K230程序中的配置已正确设置：
- WiFi SSID: "K230_Stream"
- WiFi密码: "12345678"
- ESP IP: ***********
- TCP端口: 8888

## 📁 项目文件结构

### 核心程序文件
- `k230_video_stream.py` - **K230端优化版本程序** (推荐使用)
- `esp8266_stream_server.cpp` - **ESP8266端完整程序** (独立版本)
- `src/main.cpp` - ESP8266端主程序 (PlatformIO项目版本)
- `include/config.h` - ESP8266配置文件

### 参考程序文件
- `untitled_4.py` - K230端参考程序
- `esp8266_websocket_server.ino` - Arduino IDE版本

### 文档文件
- `使用说明.md` - **详细使用说明文档**
- `README.md` - 项目概述文档

### 测试工具
- `test_system.py` - **系统功能测试脚本**

## 使用步骤

### 1. 环境准备
**ESP8266端 (VSCode + PlatformIO):**
```bash
# 安装PlatformIO扩展
# 依赖库已在platformio.ini中配置，会自动安装：
# - ESPAsyncWebServer-esphome@^3.4.0
# - ESPAsyncTCP@^2.0.0
```

**K230端 (CanMV K230):**
- 确保CanMV K230开发环境已配置
- 支持MicroPython和相关媒体库

### 2. 配置修改
编辑 `include/config.h` 文件：
```cpp
#define STA_SSID "your_router_ssid"        // 修改为您的路由器SSID
#define STA_PASSWORD "your_router_password" // 修改为您的路由器密码
```

### 3. 编译上传ESP8266程序
```bash
# 使用PlatformIO编译上传
pio run --target upload
# 或在VSCode中使用PlatformIO插件上传
```

### 4. 运行K230程序
```bash
# 推荐使用优化版本
python k230_video_stream.py

# 或使用原始版本
python untitled_4.py
```

### 5. 系统测试
```bash
# 运行系统测试脚本
python test_system.py

# 或指定ESP8266 IP地址
python test_system.py ***********
```

### 6. 访问监控页面
- **AP模式访问**: `http://***********`
- **路由器模式访问**: `http://[ESP8266的STA_IP]`
- **系统状态API**: `http://[IP]/status`
- **WebSocket连接**: `ws://[IP]/ws`

## 通信协议

### K230 -> ESP8266 (TCP Socket)
1. **帧数据格式**：
   - 4字节帧大小（小端序）
   - JPEG数据（分块发送，每块1024字节）

2. **心跳包**：
   - 每5秒发送 "HB" 字符串
   - ESP8266超时时间：10秒

### ESP8266 -> 浏览器 (AsyncWebSocket)
- 异步WebSocket连接：`/ws`
- 直接转发JPEG二进制数据
- 浏览器接收后创建Blob URL显示

## 性能参数
- 目标帧率：30 FPS
- 图像分辨率：640x480
- JPEG质量：50%
- 最大帧大小：64KB
- 缓冲区大小：64KB

## 故障排除

### 1. K230连接失败
- 检查ESP8266 AP是否正常启动
- 确认WiFi密码正确
- 查看串口输出的连接状态

### 2. 网页无法访问
- 确认ESP8266已连接到路由器
- 检查防火墙设置
- 尝试直接访问AP IP：***********

### 3. 视频流中断
- 检查K230心跳状态
- 确认网络稳定性
- 查看ESP8266内存使用情况

### 4. 帧率过低
- 降低JPEG质量参数
- 减小图像分辨率
- 检查网络带宽

## 新增功能特性

### V1.0 更新内容
**ESP8266端优化:**
- ✅ 增强的WiFi连接管理和超时处理
- ✅ 实时系统状态API (`/status`)
- ✅ 性能监控和内存管理
- ✅ 优化的WebSocket客户端管理
- ✅ 响应式Web界面，支持移动端
- ✅ 自动重连和错误恢复机制
- ✅ 帧率显示和连接状态监控

**K230端优化:**
- ✅ 增强的错误处理和状态检测
- ✅ 优化的图像捕获和压缩
- ✅ 智能重连机制
- ✅ 性能监控和帧率控制
- ✅ 内存管理优化

### 系统状态API
访问 `http://[IP]/status` 获取JSON格式的系统状态：
```json
{
  "k230_connected": true,
  "fps": 25.3,
  "clients": 2,
  "free_heap": 45632,
  "uptime": 123456
}
```

## 技术特性
- **双模WiFi**：ESP8266同时作为AP和STA，支持热点和路由器连接
- **异步处理**：使用ESPAsyncWebServer提高并发性能
- **实时传输**：AsyncWebSocket低延迟视频流传输
- **自动重连**：网络断开自动恢复，支持心跳检测
- **内存优化**：64KB循环缓冲区，智能内存管理
- **错误处理**：完善的异常处理和状态监控
- **性能监控**：实时FPS显示，内存使用监控
- **响应式界面**：支持桌面和移动端访问

## 扩展功能
- ✅ 多客户端同时观看 (最多4个)
- ✅ 实时状态监控和性能分析
- ✅ 自动重连和故障恢复
- 🔄 录像功能 (计划中)
- 🔄 双向音频传输 (计划中)
- 🔄 运动检测 (计划中)
- 🔄 用户认证 (计划中)

## 注意事项
1. **内存管理**: ESP8266内存有限(~45KB可用)，建议控制同时连接的客户端数量
2. **性能优化**: 长时间运行建议启用性能监控，观察内存使用情况
3. **网络稳定性**: 建议使用稳定的WiFi环境，避免频繁断线重连
4. **功耗管理**: 注意功耗管理，特别是电池供电场景
5. **安全考虑**: 生产环境建议加密传输和身份验证
