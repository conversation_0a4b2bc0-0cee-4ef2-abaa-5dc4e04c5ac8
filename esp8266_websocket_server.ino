#include <ESP8266WiFi.h>
#include <ESPAsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <WiFiClient.h>
#include "config.h"

// 服务器配置
AsyncWebServer server(WEB_SERVER_PORT);
AsyncWebSocket ws("/ws");
WiFiServer tcpServer(TCP_SERVER_PORT);

// 全局变量
WiFiClient k230Client;
bool k230Connected = false;
uint32_t lastHeartbeat = 0;
uint8_t* frameBuffer = nullptr;
size_t frameSize = 0;
bool newFrameReady = false;

void setup() {
  Serial.begin(SERIAL_BAUD_RATE);
  if (DEBUG_ENABLED) Serial.println("ESP8266 Video Streaming Server");

  setupWiFi();
  setupWebServer();
  setupWebSocket();
  setupTCPServer();

  frameBuffer = (uint8_t*)malloc(FRAME_BUFFER_SIZE);
  if (!frameBuffer) {
    Serial.println("内存分配失败!");
    ESP.restart();
  }
}

void setupWiFi() {
  WiFi.mode(WIFI_AP_STA);
  WiFi.softAP(AP_SSID, AP_PASSWORD);
  if (DEBUG_ENABLED) {
    Serial.print("AP IP: ");
    Serial.println(WiFi.softAPIP());
  }

  WiFi.begin(STA_SSID, STA_PASSWORD);
  if (DEBUG_ENABLED) Serial.print("连接路由器");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    if (DEBUG_ENABLED) Serial.print(".");
  }
  if (DEBUG_ENABLED) {
    Serial.println();
    Serial.print("STA IP: ");
    Serial.println(WiFi.localIP());
  }
}

void setupWebServer() {
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "text/html", getIndexHTML());
  });

  server.on("/stream", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "text/plain", "使用WebSocket /ws 获取视频流");
  });

  server.begin();
  if (DEBUG_ENABLED) Serial.println("Web服务器启动");
}

void setupWebSocket() {
  ws.onEvent(onWsEvent);
  server.addHandler(&ws);
  if (DEBUG_ENABLED) Serial.println("WebSocket服务器启动");
}

void setupTCPServer() {
  tcpServer.begin();
  Serial.println("TCP服务器启动，等待K230连接...");
}

String getIndexHTML() {
  return R"(
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>K230实时监控</title>
    <style>
        body { margin:0; padding:20px; background:#000; color:#fff; font-family:Arial; }
        .container { max-width:800px; margin:0 auto; text-align:center; }
        #video { max-width:100%; border:2px solid #333; }
        .status { margin:10px 0; padding:10px; background:#333; border-radius:5px; }
        .connected { color:#0f0; }
        .disconnected { color:#f00; }
    </style>
</head>
<body>
    <div class="container">
        <h1>K230实时视频监控</h1>
        <div class="status">
            连接状态: <span id="status" class="disconnected">未连接</span>
        </div>
        <img id="video" src="" alt="等待视频流...">
    </div>

    <script>
        const ws = new WebSocket('ws://' + window.location.hostname + '/ws');
        const video = document.getElementById('video');
        const status = document.getElementById('status');

        ws.onopen = function() {
            status.textContent = '已连接';
            status.className = 'connected';
        };

        ws.onclose = function() {
            status.textContent = '连接断开';
            status.className = 'disconnected';
        };

        ws.onmessage = function(event) {
            if (event.data instanceof Blob) {
                const url = URL.createObjectURL(event.data);
                video.src = url;
                setTimeout(() => URL.revokeObjectURL(url), 100);
            }
        };

        ws.onerror = function() {
            status.textContent = '连接错误';
            status.className = 'disconnected';
        };
    </script>
</body>
</html>
)";
}

void onWsEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len) {
  switch(type) {
    case WS_EVT_CONNECT:
      if (DEBUG_ENABLED) Serial.printf("客户端[%u]连接\n", client->id());
      break;
    case WS_EVT_DISCONNECT:
      if (DEBUG_ENABLED) Serial.printf("客户端[%u]断开连接\n", client->id());
      break;
    default:
      break;
  }
}

void handleK230Connection() {
  if (!k230Client.connected()) {
    k230Client = tcpServer.available();
    if (k230Client) {
      Serial.println("K230已连接");
      k230Connected = true;
      lastHeartbeat = millis();
    }
  }
}

bool receiveFrame() {
  if (!k230Client || !k230Client.connected()) return false;
  
  // 检查心跳
  if (k230Client.available() >= 2) {
    uint8_t hb[2];
    if (k230Client.peek() == 'H') {
      k230Client.readBytes(hb, 2);
      if (hb[0] == 'H' && hb[1] == 'B') {
        lastHeartbeat = millis();
        return false;
      }
    }
  }
  
  // 接收帧大小
  if (k230Client.available() < 4) return false;
  
  uint8_t sizeBytes[4];
  k230Client.readBytes(sizeBytes, 4);
  uint32_t size = sizeBytes[0] | (sizeBytes[1] << 8) | (sizeBytes[2] << 16) | (sizeBytes[3] << 24);
  
  if (size > MAX_FRAME_SIZE) {
    if (DEBUG_ENABLED) Serial.println("帧过大，跳过");
    return false;
  }
  
  // 接收帧数据
  uint32_t received = 0;
  uint32_t startTime = millis();
  
  while (received < size && (millis() - startTime) < FRAME_RECEIVE_TIMEOUT) {
    int available = k230Client.available();
    if (available > 0) {
      int toRead = min(available, (int)(size - received));
      int bytesRead = k230Client.readBytes(frameBuffer + received, toRead);
      received += bytesRead;
    }
    yield();
  }
  
  if (received == size) {
    frameSize = size;
    newFrameReady = true;
    return true;
  }
  
  return false;
}

void broadcastFrame() {
  if (!newFrameReady || frameSize == 0) return;

  ws.binaryAll(frameBuffer, frameSize);
  newFrameReady = false;
}

void checkK230Status() {
  if (k230Connected && (millis() - lastHeartbeat > HEARTBEAT_TIMEOUT)) {
    if (DEBUG_ENABLED) Serial.println("K230心跳超时，断开连接");
    k230Client.stop();
    k230Connected = false;
  }
}

void loop() {
  ws.cleanupClients();

  handleK230Connection();

  if (k230Connected) {
    if (receiveFrame()) {
      broadcastFrame();
    }
    checkK230Status();
  }

  yield();
}
