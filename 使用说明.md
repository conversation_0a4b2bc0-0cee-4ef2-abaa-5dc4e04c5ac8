# ESP8266 + K230 实时图像传输监控系统 - 使用说明

## 📋 系统概述

本系统实现了基于ESP8266和K230的实时图像传输监控功能，具有以下特点：

- **ESP8266**: 作为Web服务器，使用STA+AP双模式
- **K230**: 作为图像采集端，通过Socket发送JPEG编码流  
- **WebSocket**: 实现实时图像传输到网页端
- **响应式界面**: 支持桌面和移动端访问

## 🏗️ 系统架构

```
K230设备 ──WiFi──> ESP8266(AP+STA) ──WebSocket──> 网页客户端
   ↓                    ↓                      ↓
图像采集            视频流转发              实时显示
JPEG编码            状态监控              多客户端支持
```

## 📁 文件说明

### 核心程序文件
- `k230_video_stream.py` - K230端优化版本程序
- `esp8266_stream_server.cpp` - ESP8266端完整程序
- `src/main.cpp` - ESP8266端主程序(PlatformIO版本)
- `include/config.h` - ESP8266配置文件

### 参考文件
- `untitled_4.py` - K230端参考程序
- `esp8266_websocket_server.ino` - Arduino IDE版本

### 配置文件
- `platformio.ini` - PlatformIO项目配置
- `README.md` - 项目说明文档

## 🚀 快速开始

### 1. 环境准备

**ESP8266端 (推荐VSCode + PlatformIO):**
```bash
# 安装VSCode
# 安装PlatformIO扩展
# 依赖库会自动安装
```

**K230端 (CanMV K230):**
```bash
# 确保CanMV K230开发环境已配置
# 支持MicroPython和媒体库
```

### 2. 配置修改

编辑 `include/config.h` 文件：
```cpp
// 修改为您的路由器信息
#define STA_SSID "your_router_ssid"        
#define STA_PASSWORD "your_router_password"

// AP热点配置(通常不需要修改)
#define AP_SSID "K230_Stream"              
#define AP_PASSWORD "12345678"             
```

### 3. 编译上传ESP8266程序

**使用PlatformIO (推荐):**
```bash
# 在项目根目录执行
pio run --target upload

# 或在VSCode中使用PlatformIO插件
# 点击"Upload"按钮
```

**使用Arduino IDE:**
```bash
# 打开esp8266_websocket_server.ino
# 选择正确的ESP8266开发板
# 编译并上传
```

### 4. 运行K230程序

```bash
# 将k230_video_stream.py上传到K230设备
# 在CanMV K230中运行
python k230_video_stream.py
```

### 5. 访问监控页面

- **AP模式**: `http://***********`
- **路由器模式**: `http://[ESP8266的IP地址]`
- **系统状态API**: `http://[IP]/status`

## 🔧 配置参数说明

### ESP8266端配置 (config.h)

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `STA_SSID` | "your_router_ssid" | 路由器SSID |
| `STA_PASSWORD` | "your_router_password" | 路由器密码 |
| `AP_SSID` | "K230_Stream" | AP热点名称 |
| `AP_PASSWORD` | "12345678" | AP热点密码 |
| `WEB_SERVER_PORT` | 80 | Web服务器端口 |
| `TCP_SERVER_PORT` | 8888 | K230连接端口 |
| `FRAME_BUFFER_SIZE` | 65536 | 帧缓冲区大小(64KB) |
| `HEARTBEAT_TIMEOUT` | 10000 | 心跳超时时间(ms) |

### K230端配置 (k230_video_stream.py)

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `WIFI_SSID` | "K230_Stream" | 连接的WiFi名称 |
| `WIFI_PASSWORD` | "12345678" | WiFi密码 |
| `ESP_IP` | "***********" | ESP8266 IP地址 |
| `ESP_PORT` | 8888 | 连接端口 |
| `JPEG_QUALITY` | 50 | JPEG压缩质量(1-100) |
| `TARGET_FPS` | 30 | 目标帧率 |
| `MAX_RETRIES` | 10 | 最大重连次数 |

## 📊 系统状态监控

### Web界面功能
- ✅ 实时视频流显示
- ✅ 连接状态监控
- ✅ 帧率显示
- ✅ 客户端数量显示
- ✅ 内存使用监控
- ✅ 自动重连功能
- ✅ 全屏显示支持

### 状态API
访问 `/status` 端点获取JSON格式状态信息：
```json
{
  "k230_connected": true,
  "fps": 25.3,
  "clients": 2,
  "free_heap": 45632,
  "uptime": 123456
}
```

## 🔍 故障排除

### 常见问题及解决方案

**1. K230连接失败**
```bash
# 检查项目：
- ESP8266 AP是否正常启动
- WiFi密码是否正确
- 查看串口输出的连接状态
- 确认K230和ESP8266在同一网络
```

**2. 网页无法访问**
```bash
# 检查项目：
- ESP8266是否已连接到路由器
- 防火墙设置是否阻止访问
- 尝试直接访问AP IP：***********
- 检查Web服务器是否正常启动
```

**3. 视频流中断**
```bash
# 检查项目：
- K230心跳状态是否正常
- 网络连接是否稳定
- ESP8266内存使用情况
- 查看串口调试信息
```

**4. 帧率过低**
```bash
# 优化方案：
- 降低JPEG_QUALITY参数(30-70)
- 减小图像分辨率
- 检查网络带宽
- 减少同时连接的客户端数量
```

**5. 内存不足**
```bash
# 解决方案：
- 重启ESP8266设备
- 减少WebSocket客户端连接数
- 检查是否有内存泄漏
- 启用性能监控查看内存使用
```

## ⚡ 性能优化建议

### ESP8266端优化
1. **内存管理**: 定期监控内存使用，避免内存泄漏
2. **客户端限制**: 控制同时连接的WebSocket客户端数量(建议≤4)
3. **帧率控制**: 根据网络条件调整目标帧率
4. **缓冲区优化**: 根据实际需求调整帧缓冲区大小

### K230端优化
1. **图像质量**: 平衡JPEG质量和传输速度
2. **错误处理**: 启用自动重连和错误恢复
3. **内存清理**: 定期执行垃圾回收
4. **网络稳定**: 确保WiFi信号强度充足

### 网络优化
1. **信道选择**: 选择干扰较少的WiFi信道
2. **距离控制**: 保持设备间合理距离
3. **带宽管理**: 避免网络拥塞
4. **QoS设置**: 为视频流分配优先级

## 🔒 安全注意事项

1. **密码安全**: 修改默认的AP密码
2. **网络隔离**: 生产环境建议使用独立网络
3. **访问控制**: 考虑添加用户认证机制
4. **数据加密**: 敏感环境建议启用HTTPS/WSS
5. **固件更新**: 定期更新设备固件

## 📞 技术支持

如遇到问题，请提供以下信息：
- 设备型号和固件版本
- 网络环境配置
- 错误日志和串口输出
- 问题复现步骤

---

**版本**: v1.0  
**更新时间**: 2024年  
**开发环境**: VSCode + PlatformIO + CanMV K230
