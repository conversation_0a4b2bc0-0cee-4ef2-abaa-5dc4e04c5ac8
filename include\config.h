#ifndef CONFIG_H
#define CONFIG_H

// WiFi配置 - 统一配置管理
#define STA_SSID "your_router_ssid"        // 修改为您的路由器SSID
#define STA_PASSWORD "your_router_password" // 修改为您的路由器密码
#define AP_SSID "K230_Stream"              // AP热点名称
#define AP_PASSWORD "12345678"             // AP热点密码
#define AP_CHANNEL 6                       // AP信道
#define AP_MAX_CONNECTIONS 4               // AP最大连接数

// 服务器端口配置
#define WEB_SERVER_PORT 80                 // Web服务器端口
#define TCP_SERVER_PORT 8888               // K230连接端口

// 缓冲区配置 - 性能优化
#define FRAME_BUFFER_SIZE 65536            // 64KB帧缓冲区
#define MAX_FRAME_SIZE 65536               // 最大帧大小
#define WEBSOCKET_MAX_CLIENTS 4            // WebSocket最大客户端数

// 超时配置
#define HEARTBEAT_TIMEOUT 10000            // 心跳超时时间(ms)
#define FRAME_RECEIVE_TIMEOUT 5000         // 帧接收超时时间(ms)
#define WIFI_CONNECT_TIMEOUT 30000         // WiFi连接超时(ms)

// 性能配置
#define FRAME_RATE_LIMIT 30                // 帧率限制(FPS)
#define MEMORY_CHECK_INTERVAL 5000         // 内存检查间隔(ms)
#define CLIENT_CLEANUP_INTERVAL 1000       // 客户端清理间隔(ms)

// 调试配置
#define DEBUG_ENABLED true                 // 启用调试输出
#define SERIAL_BAUD_RATE 115200           // 串口波特率
#define PERFORMANCE_MONITOR true           // 性能监控

#endif
