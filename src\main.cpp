/**
 * ESP8266实时视频流服务器 - 完整优化版本
 * 基于ESP8266 + ESPAsyncWebServer实现WebSocket视频流传输
 * 支持STA+AP双模式，接收K230发送的JPEG流并转发到网页
 * 
 * 作者: AI Assistant
 * 版本: 1.0
 * 开发环境: VSCode + PlatformIO
 */

#include <ESP8266WiFi.h>
#include <ESPAsyncTCP.h>
#include <ESPAsyncWebServer.h>
#include <WiFiClient.h>

// ==================== 配置参数 ====================
// WiFi配置 - 根据实际环境修改
#define STA_SSID "your_router_ssid"        // 路由器SSID
#define STA_PASSWORD "your_router_password" // 路由器密码
#define AP_SSID "K230_Stream"              // AP热点名称
#define AP_PASSWORD "12345678"             // AP热点密码
#define AP_CHANNEL 6                       // AP信道
#define AP_MAX_CONNECTIONS 4               // AP最大连接数

// 服务器端口配置
#define WEB_SERVER_PORT 80                 // Web服务器端口
#define TCP_SERVER_PORT 8888               // K230连接端口

// 缓冲区配置
#define FRAME_BUFFER_SIZE 65536            // 64KB帧缓冲区
#define MAX_FRAME_SIZE 65536               // 最大帧大小
#define WEBSOCKET_MAX_CLIENTS 4            // WebSocket最大客户端数

// 超时配置
#define HEARTBEAT_TIMEOUT 10000            // 心跳超时时间(ms)
#define FRAME_RECEIVE_TIMEOUT 5000         // 帧接收超时时间(ms)
#define WIFI_CONNECT_TIMEOUT 30000         // WiFi连接超时(ms)

// 性能配置
#define FRAME_RATE_LIMIT 30                // 帧率限制(FPS)
#define MEMORY_CHECK_INTERVAL 5000         // 内存检查间隔(ms)
#define CLIENT_CLEANUP_INTERVAL 1000       // 客户端清理间隔(ms)

// 调试配置
#define DEBUG_ENABLED true                 // 启用调试输出
#define SERIAL_BAUD_RATE 115200           // 串口波特率
#define PERFORMANCE_MONITOR true           // 性能监控

// ==================== 全局变量 ====================
// 服务器配置
AsyncWebServer server(WEB_SERVER_PORT);
AsyncWebSocket ws("/ws");
WiFiServer tcpServer(TCP_SERVER_PORT);

// 系统状态管理
WiFiClient k230Client;
bool k230Connected = false;
uint32_t lastHeartbeat = 0;
uint8_t* frameBuffer = nullptr;
size_t frameSize = 0;
bool newFrameReady = false;

// 性能监控变量
uint32_t frameCount = 0;
uint32_t lastFrameTime = 0;
uint32_t lastMemoryCheck = 0;
uint32_t lastClientCleanup = 0;
float currentFPS = 0.0;

// ==================== 核心功能函数 ====================

void setup() {
  Serial.begin(SERIAL_BAUD_RATE);
  if (DEBUG_ENABLED) {
    Serial.println("\n" + String("=").repeat(50));
    Serial.println("🚀 ESP8266实时视频流服务器 v1.0");
    Serial.println(String("=").repeat(50));
    Serial.printf("编译时间: %s %s\n", __DATE__, __TIME__);
    Serial.printf("可用内存: %d bytes\n", ESP.getFreeHeap());
  }

  // 初始化各个模块
  setupWiFi();
  setupWebServer();
  setupWebSocket();
  setupTCPServer();

  // 分配帧缓冲区 - 内存管理优化
  frameBuffer = (uint8_t*)malloc(FRAME_BUFFER_SIZE);
  if (!frameBuffer) {
    Serial.println("❌ 错误: 内存分配失败!");
    Serial.printf("可用内存: %d bytes\n", ESP.getFreeHeap());
    ESP.restart();
  }
  
  if (DEBUG_ENABLED) {
    Serial.printf("✅ 帧缓冲区分配成功: %d bytes\n", FRAME_BUFFER_SIZE);
    Serial.printf("剩余内存: %d bytes\n", ESP.getFreeHeap());
    Serial.println("🎉 系统初始化完成\n");
  }
}

void setupWiFi() {
  // 设置双模WiFi - AP+STA模式
  WiFi.mode(WIFI_AP_STA);
  
  // 配置AP模式 - 增强配置
  WiFi.softAP(AP_SSID, AP_PASSWORD, AP_CHANNEL, false, AP_MAX_CONNECTIONS);
  if (DEBUG_ENABLED) {
    Serial.printf("📡 AP模式启动: %s\n", AP_SSID);
    Serial.printf("📍 AP IP: %s\n", WiFi.softAPIP().toString().c_str());
    Serial.printf("📊 AP信道: %d | 最大连接数: %d\n", AP_CHANNEL, AP_MAX_CONNECTIONS);
  }

  // 连接路由器 - 增强错误处理
  WiFi.begin(STA_SSID, STA_PASSWORD);
  if (DEBUG_ENABLED) Serial.printf("🔗 正在连接路由器: %s", STA_SSID);
  
  uint32_t startTime = millis();
  while (WiFi.status() != WL_CONNECTED) {
    if (millis() - startTime > WIFI_CONNECT_TIMEOUT) {
      if (DEBUG_ENABLED) {
        Serial.println("\n⚠️  路由器连接超时，仅使用AP模式");
      }
      return;
    }
    delay(500);
    if (DEBUG_ENABLED) Serial.print(".");
  }
  
  if (DEBUG_ENABLED) {
    Serial.println();
    Serial.printf("✅ STA连接成功: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("📶 信号强度: %d dBm\n", WiFi.RSSI());
  }
}

void setupWebServer() {
  // 主页路由
  server.on("/", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "text/html", getIndexHTML());
  });

  // 状态信息路由 - 新增系统状态API
  server.on("/status", HTTP_GET, [](AsyncWebServerRequest *request){
    String status = "{";
    status += "\"k230_connected\":" + String(k230Connected ? "true" : "false") + ",";
    status += "\"fps\":" + String(currentFPS, 1) + ",";
    status += "\"clients\":" + String(ws.count()) + ",";
    status += "\"free_heap\":" + String(ESP.getFreeHeap()) + ",";
    status += "\"uptime\":" + String(millis()) + "";
    status += "}";
    request->send(200, "application/json", status);
  });

  // 流信息路由
  server.on("/stream", HTTP_GET, [](AsyncWebServerRequest *request){
    request->send(200, "text/plain", "使用WebSocket /ws 获取视频流");
  });

  server.begin();
  if (DEBUG_ENABLED) Serial.printf("🌐 Web服务器启动 - 端口: %d\n", WEB_SERVER_PORT);
}

void setupWebSocket() {
  ws.onEvent(onWsEvent);
  server.addHandler(&ws);
  if (DEBUG_ENABLED) Serial.printf("🔌 WebSocket服务器启动 - 路径: /ws\n");
}

void setupTCPServer() {
  tcpServer.begin();
  if (DEBUG_ENABLED) Serial.printf("🖥️  TCP服务器启动 - 端口: %d\n", TCP_SERVER_PORT);
  Serial.println("⏳ 等待K230连接...");
}

String getIndexHTML() {
  String html = "<!DOCTYPE html><html><head><meta charset='UTF-8'>";
  html += "<title>K230实时视频监控系统</title>";
  html += "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
  html += "<style>";
  html += "* { margin:0; padding:0; box-sizing:border-box; }";
  html += "body { background:#111; color:#fff; font-family:'Segoe UI',Arial,sans-serif; }";
  html += ".container { max-width:1200px; margin:0 auto; padding:20px; }";
  html += ".header { text-align:center; margin-bottom:20px; }";
  html += ".header h1 { color:#4CAF50; margin-bottom:10px; }";
  html += ".status-bar { display:flex; justify-content:space-between; margin-bottom:20px; padding:15px; background:#222; border-radius:8px; flex-wrap:wrap; }";
  html += ".status-item { margin:5px 0; }";
  html += ".status-item span { font-weight:bold; }";
  html += ".connected { color:#4CAF50; }";
  html += ".disconnected { color:#f44336; }";
  html += ".video-container { position:relative; background:#000; border-radius:8px; overflow:hidden; margin-bottom:20px; }";
  html += "#video { width:100%; height:auto; display:block; }";
  html += ".controls { text-align:center; }";
  html += ".btn { background:#4CAF50; color:white; border:none; padding:10px 20px; margin:5px; border-radius:5px; cursor:pointer; }";
  html += ".btn:hover { background:#45a049; }";
  html += ".btn:disabled { background:#666; cursor:not-allowed; }";
  html += "@media (max-width: 768px) { .status-bar { flex-direction:column; } }";
  html += "</style></head><body>";
  html += "<div class='container'>";
  html += "<div class='header'>";
  html += "<h1>K230实时视频监控系统</h1>";
  html += "<p>基于ESP8266 + K230的高性能视频流传输</p>";
  html += "</div>";
  html += "<div class='status-bar'>";
  html += "<div class='status-item'>连接状态: <span id='status' class='disconnected'>未连接</span></div>";
  html += "<div class='status-item'>帧率: <span id='fps'>0</span> FPS</div>";
  html += "<div class='status-item'>客户端: <span id='clients'>0</span></div>";
  html += "<div class='status-item'>内存: <span id='memory'>0</span> KB</div>";
  html += "</div>";
  html += "<div class='video-container'>";
  html += "<img id='video' src='' alt='等待视频流...' style='width:100%; height:400px; object-fit:contain; background:#000;'>";
  html += "</div>";
  html += "<div class='controls'>";
  html += "<button class='btn' onclick='reconnect()'>重新连接</button>";
  html += "<button class='btn' onclick='toggleFullscreen()'>全屏</button>";
  html += "</div></div>";
  html += "<script>";
  html += "let ws = null;";
  html += "const video = document.getElementById('video');";
  html += "const status = document.getElementById('status');";
  html += "const fps = document.getElementById('fps');";
  html += "const clients = document.getElementById('clients');";
  html += "const memory = document.getElementById('memory');";
  html += "function connect() {";
  html += "ws = new WebSocket('ws://' + window.location.hostname + '/ws');";
  html += "ws.onopen = function() { status.textContent = '已连接'; status.className = 'connected'; };";
  html += "ws.onclose = function() { status.textContent = '连接断开'; status.className = 'disconnected'; setTimeout(connect, 3000); };";
  html += "ws.onmessage = function(event) { if (event.data instanceof Blob) { const url = URL.createObjectURL(event.data); video.src = url; setTimeout(() => URL.revokeObjectURL(url), 100); } };";
  html += "ws.onerror = function() { status.textContent = '连接错误'; status.className = 'disconnected'; };";
  html += "}";
  html += "function updateStatus() {";
  html += "fetch('/status').then(response => response.json()).then(data => {";
  html += "fps.textContent = data.fps || '0';";
  html += "clients.textContent = data.clients || '0';";
  html += "memory.textContent = Math.round(data.free_heap / 1024) || '0';";
  html += "}).catch(() => {});";
  html += "}";
  html += "function reconnect() { if (ws) ws.close(); connect(); }";
  html += "function toggleFullscreen() { if (!document.fullscreenElement) { video.requestFullscreen(); } else { document.exitFullscreen(); } }";
  html += "connect(); setInterval(updateStatus, 1000);";
  html += "</script></body></html>";
  return html;
}

void onWsEvent(AsyncWebSocket *server, AsyncWebSocketClient *client, AwsEventType type, void *arg, uint8_t *data, size_t len) {
  switch(type) {
    case WS_EVT_CONNECT:
      if (DEBUG_ENABLED) {
        Serial.printf("🔗 WebSocket客户端连接: ID[%u] IP[%s]\n",
                     client->id(), client->remoteIP().toString().c_str());
        Serial.printf("👥 当前连接数: %d\n", ws.count());
      }
      break;

    case WS_EVT_DISCONNECT:
      if (DEBUG_ENABLED) {
        Serial.printf("❌ WebSocket客户端断开: ID[%u]\n", client->id());
        Serial.printf("👥 当前连接数: %d\n", ws.count());
      }
      break;

    case WS_EVT_ERROR:
      if (DEBUG_ENABLED) {
        Serial.printf("⚠️  WebSocket错误: ID[%u]\n", client->id());
      }
      break;

    default:
      break;
  }
}

void handleK230Connection() {
  if (!k230Client.connected()) {
    k230Client = tcpServer.accept();
    if (k230Client) {
      if (DEBUG_ENABLED) {
        Serial.printf("📱 K230设备连接: IP[%s]\n", k230Client.remoteIP().toString().c_str());
      }
      k230Connected = true;
      lastHeartbeat = millis();
      frameCount = 0;  // 重置帧计数
      currentFPS = 0.0;
    }
  }
}

bool receiveFrame() {
  if (!k230Client || !k230Client.connected()) return false;

  // 检查心跳包 - 优化心跳处理
  if (k230Client.available() >= 2) {
    uint8_t hb[2];
    if (k230Client.peek() == 'H') {
      k230Client.readBytes(hb, 2);
      if (hb[0] == 'H' && hb[1] == 'B') {
        lastHeartbeat = millis();
        if (DEBUG_ENABLED && PERFORMANCE_MONITOR) {
          Serial.println("💓 收到心跳包");
        }
        return false;
      }
    }
  }

  // 接收帧大小 - 增强错误检测
  if (k230Client.available() < 4) return false;

  uint8_t sizeBytes[4];
  int bytesRead = k230Client.readBytes(sizeBytes, 4);
  if (bytesRead != 4) {
    if (DEBUG_ENABLED) Serial.println("❌ 帧大小读取失败");
    return false;
  }

  uint32_t size = sizeBytes[0] | (sizeBytes[1] << 8) | (sizeBytes[2] << 16) | (sizeBytes[3] << 24);

  if (size == 0 || size > MAX_FRAME_SIZE) {
    if (DEBUG_ENABLED) Serial.printf("⚠️  无效帧大小: %u bytes\n", size);
    return false;
  }

  // 接收帧数据 - 优化接收效率
  uint32_t received = 0;
  uint32_t startTime = millis();

  while (received < size && (millis() - startTime) < FRAME_RECEIVE_TIMEOUT) {
    int available = k230Client.available();
    if (available > 0) {
      int toRead = min(available, (int)(size - received));
      int actualRead = k230Client.readBytes(frameBuffer + received, toRead);
      if (actualRead <= 0) break;
      received += actualRead;
    }
    yield();  // 让出CPU时间
  }

  if (received == size) {
    frameSize = size;
    newFrameReady = true;
    frameCount++;  // 增加帧计数

    // 计算FPS - 性能监控
    uint32_t currentTime = millis();
    if (currentTime - lastFrameTime >= 1000) {
      currentFPS = frameCount * 1000.0 / (currentTime - lastFrameTime);
      if (DEBUG_ENABLED && PERFORMANCE_MONITOR) {
        Serial.printf("📊 FPS: %.1f | 帧大小: %.1fKB\n", currentFPS, size/1024.0);
      }
      frameCount = 0;
      lastFrameTime = currentTime;
    }

    return true;
  } else {
    if (DEBUG_ENABLED) {
      Serial.printf("⚠️  帧接收不完整: %u/%u bytes\n", received, size);
    }
    return false;
  }
}

void broadcastFrame() {
  if (!newFrameReady || frameSize == 0 || ws.count() == 0) return;

  // 广播到所有WebSocket客户端 - 优化传输
  ws.binaryAll(frameBuffer, frameSize);
  newFrameReady = false;

  if (DEBUG_ENABLED && PERFORMANCE_MONITOR && frameCount % 30 == 0) {
    Serial.printf("📡 广播帧: %d bytes 到 %d 个客户端\n", frameSize, ws.count());
  }
}

void checkK230Status() {
  if (k230Connected && (millis() - lastHeartbeat > HEARTBEAT_TIMEOUT)) {
    if (DEBUG_ENABLED) {
      Serial.printf("💔 K230心跳超时 (%lu ms)，断开连接\n", millis() - lastHeartbeat);
    }
    k230Client.stop();
    k230Connected = false;
    currentFPS = 0.0;
    frameCount = 0;
  }
}

void performanceMonitor() {
  uint32_t currentTime = millis();

  // 内存检查
  if (currentTime - lastMemoryCheck >= MEMORY_CHECK_INTERVAL) {
    uint32_t freeHeap = ESP.getFreeHeap();
    if (DEBUG_ENABLED && PERFORMANCE_MONITOR) {
      Serial.printf("🧠 内存状态: %d bytes 可用\n", freeHeap);
    }

    // 内存不足警告
    if (freeHeap < 8192) {  // 8KB阈值
      Serial.println("⚠️  警告: 内存不足!");
    }

    lastMemoryCheck = currentTime;
  }
}

void loop() {
  // 定期清理WebSocket客户端
  uint32_t currentTime = millis();
  if (currentTime - lastClientCleanup >= CLIENT_CLEANUP_INTERVAL) {
    ws.cleanupClients();
    lastClientCleanup = currentTime;
  }

  // 处理K230连接
  handleK230Connection();

  // 处理数据传输
  if (k230Connected) {
    if (receiveFrame()) {
      broadcastFrame();
    }
    checkK230Status();
  }

  // 性能监控
  if (PERFORMANCE_MONITOR) {
    performanceMonitor();
  }

  yield();  // 让出CPU时间
}
