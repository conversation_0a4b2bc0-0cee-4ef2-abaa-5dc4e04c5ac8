import time, os, sys, socket, ustruct, network
from media.sensor import Sensor
from media.media import MediaManager
import image
import gc

# 配置参数 - 统一配置管理
WIFI_SSID = "K230_Stream"  # ESP8266 AP热点名称
WIFI_PASSWORD = "12345678"  # ESP8266 AP热点密码
ESP_IP = "***********"  # ESP8266 AP模式固定IP
ESP_PORT = 8888  # TCP通信端口

# 图像参数 - 性能优化配置
IMAGE_WIDTH = 640  # 图像宽度
IMAGE_HEIGHT = 480  # 图像高度
JPEG_QUALITY = 50  # JPEG压缩质量(1-100)
TARGET_FPS = 30  # 目标帧率
MAX_RETRIES = 10  # 最大重连次数
MAX_ERRORS = 20  # 最大错误次数
HEARTBEAT_INTERVAL = 5000  # 心跳间隔(ms)
WIFI_TIMEOUT = 15000  # WiFi连接超时(ms)
SOCKET_TIMEOUT = 5  # Socket超时(s)
CHUNK_SIZE = 1024  # 数据块大小

def connect_to_wifi(ssid, password):
    """连接WiFi网络 - 增强错误处理和状态检测"""
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)

    if wlan.isconnected():
        print(f"已连接到WiFi: {wlan.ifconfig()[0]}")
        return True

    print(f"正在连接WiFi: {ssid}")
    wlan.connect(ssid, password)

    start_time = time.ticks_ms()
    while not wlan.isconnected():
        if time.ticks_ms() - start_time > WIFI_TIMEOUT:
            print("WiFi连接超时")
            return False
        time.sleep_ms(100)

    print(f"WiFi连接成功: {wlan.ifconfig()[0]}")
    return True

def setup_camera():
    """初始化摄像头 - 优化配置和错误处理"""
    try:
        sensor = Sensor(id=2)
        sensor.reset()
        sensor.set_framesize(Sensor.VGA, chn=0)  # 640x480分辨率
        sensor.set_pixformat(Sensor.RGB565, chn=0)  # RGB565格式
        MediaManager.init()
        sensor.run()
        print("摄像头初始化成功")
        return sensor
    except Exception as e:
        print(f"摄像头初始化失败: {e}")
        return None

def capture_and_compress(sensor):
    """捕获图像并压缩为JPEG - 增强错误处理"""
    try:
        img = sensor.snapshot(chn=0)
        if img is None:
            return None
        jpeg_data = img.compress(quality=JPEG_QUALITY, to_bytes=True)
        return jpeg_data
    except Exception as e:
        print(f"图像捕获失败: {e}")
        return None

def connect_to_esp():
    """连接ESP8266服务器 - 增强重连机制"""
    retry = 0
    while retry < MAX_RETRIES:
        try:
            print(f"尝试连接ESP8266 ({retry+1}/{MAX_RETRIES})")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(SOCKET_TIMEOUT)
            sock.connect((ESP_IP, ESP_PORT))
            sock.settimeout(None)  # 连接后取消超时
            print("ESP8266连接成功")
            return sock
        except Exception as e:
            print(f"连接失败: {e}")
            retry += 1
            if retry < MAX_RETRIES:
                time.sleep(2)
    print("ESP8266连接失败，已达最大重试次数")
    return None

def send_jpeg_data(sock, data):
    """发送JPEG数据 - 优化传输效率和错误处理"""
    if not sock or not data:
        return False

    try:
        # 发送帧大小 (4字节小端序)
        size_bytes = ustruct.pack("<I", len(data))
        sock.send(size_bytes)

        # 分块发送数据 - 优化传输效率
        total_sent = 0
        while total_sent < len(data):
            chunk_end = min(total_sent + CHUNK_SIZE, len(data))
            chunk = data[total_sent:chunk_end]
            sent = sock.send(chunk)
            if sent == 0:
                return False
            total_sent += sent
        return True
    except Exception as e:
        print(f"数据发送失败: {e}")
        return False

def send_heartbeat(sock):
    """发送心跳包 - 保持连接活跃"""
    try:
        sock.send(b"HB")
        return True
    except Exception as e:
        print(f"心跳发送失败: {e}")
        return False

def main():
    """主函数 - 完整的视频流传输系统"""
    print("=== K230实时视频流传输系统 ===")

    # 1. WiFi连接
    if not connect_to_wifi(WIFI_SSID, WIFI_PASSWORD):
        print("错误: WiFi连接失败")
        return

    # 2. 摄像头初始化
    sensor = setup_camera()
    if not sensor:
        print("错误: 摄像头初始化失败")
        return
    time.sleep(1)  # 等待摄像头稳定

    # 3. 连接ESP8266
    sock = connect_to_esp()
    if not sock:
        print("错误: ESP8266连接失败")
        sensor.stop()
        MediaManager.deinit()
        return

    # 4. 开始视频流传输
    frame_count = 0
    error_count = 0
    last_time = time.ticks_ms()
    last_heartbeat = time.ticks_ms()
    last_frame_size = 0

    print("开始视频流传输...")
    try:
        while True:
            start_time = time.ticks_ms()

            # 心跳检测 - 保持连接
            if time.ticks_ms() - last_heartbeat > HEARTBEAT_INTERVAL:
                if not send_heartbeat(sock):
                    print("心跳失败，尝试重连...")
                    sock.close()
                    sock = connect_to_esp()
                    if not sock:
                        break
                last_heartbeat = time.ticks_ms()

            # 捕获和发送图像
            jpeg_data = capture_and_compress(sensor)
            if jpeg_data:
                if send_jpeg_data(sock, jpeg_data):
                    frame_count += 1
                    last_frame_size = len(jpeg_data)
                    error_count = 0  # 重置错误计数
                else:
                    error_count += 1
                    if error_count >= MAX_ERRORS:
                        print("错误次数过多，尝试重连...")
                        sock.close()
                        sock = connect_to_esp()
                        if not sock:
                            break
                        error_count = 0

            # 显示状态信息
            current_time = time.ticks_ms()
            if current_time - last_time >= 1000:
                fps = frame_count * 1000 / (current_time - last_time)
                print(f"FPS: {fps:.1f} | 帧大小: {last_frame_size/1024:.1f}KB | 错误: {error_count}")
                frame_count = 0
                last_time = current_time

            # 帧率控制 - 性能优化
            elapsed = time.ticks_ms() - start_time
            delay = max(0, int(1000/TARGET_FPS - elapsed))
            if delay > 0:
                time.sleep_ms(delay)

            # 内存管理
            if frame_count % 10 == 0:  # 每10帧清理一次
                gc.collect()

    except KeyboardInterrupt:
        print("\n用户中断，正在退出...")
    except Exception as e:
        print(f"系统错误: {e}")
    finally:
        # 清理资源
        if sock:
            sock.close()
        if sensor:
            sensor.stop()
        MediaManager.deinit()
        print("系统已退出")

if __name__ == "__main__":
    main()
