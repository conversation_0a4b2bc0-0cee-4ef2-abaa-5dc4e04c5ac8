#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ESP8266 + K230 实时图像传输系统测试脚本
用于验证系统各组件功能是否正常
"""

import requests
import json
import time
import socket
import sys

# 测试配置
ESP8266_IP = "***********"  # ESP8266 IP地址
WEB_PORT = 80               # Web服务器端口
TCP_PORT = 8888             # TCP服务器端口
TEST_TIMEOUT = 5            # 测试超时时间(秒)

def print_header(title):
    """打印测试标题"""
    print("\n" + "="*50)
    print(f"🧪 {title}")
    print("="*50)

def print_result(test_name, success, message=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{test_name}: {status}")
    if message:
        print(f"   详情: {message}")

def test_network_connectivity():
    """测试网络连通性"""
    print_header("网络连通性测试")
    
    # 测试ping连通性
    try:
        import subprocess
        result = subprocess.run(['ping', '-c', '1', ESP8266_IP], 
                              capture_output=True, text=True, timeout=5)
        ping_success = result.returncode == 0
        print_result("Ping测试", ping_success, 
                    "网络连通正常" if ping_success else "无法ping通ESP8266")
    except Exception as e:
        print_result("Ping测试", False, f"测试异常: {e}")

def test_web_server():
    """测试Web服务器"""
    print_header("Web服务器测试")
    
    # 测试主页访问
    try:
        response = requests.get(f"http://{ESP8266_IP}:{WEB_PORT}/", timeout=TEST_TIMEOUT)
        main_page_success = response.status_code == 200
        print_result("主页访问", main_page_success, 
                    f"状态码: {response.status_code}")
        
        # 检查页面内容
        if main_page_success:
            content_check = "K230实时视频监控" in response.text
            print_result("页面内容检查", content_check,
                        "包含预期内容" if content_check else "缺少预期内容")
    except Exception as e:
        print_result("主页访问", False, f"连接异常: {e}")

def test_status_api():
    """测试状态API"""
    print_header("状态API测试")
    
    try:
        response = requests.get(f"http://{ESP8266_IP}:{WEB_PORT}/status", timeout=TEST_TIMEOUT)
        api_success = response.status_code == 200
        print_result("状态API访问", api_success, 
                    f"状态码: {response.status_code}")
        
        if api_success:
            try:
                status_data = response.json()
                required_fields = ['k230_connected', 'fps', 'clients', 'free_heap', 'uptime']
                
                for field in required_fields:
                    field_exists = field in status_data
                    print_result(f"字段 '{field}'", field_exists,
                               f"值: {status_data.get(field, 'N/A')}")
                
                # 显示系统状态
                if all(field in status_data for field in required_fields):
                    print(f"\n📊 系统状态摘要:")
                    print(f"   K230连接: {'是' if status_data['k230_connected'] else '否'}")
                    print(f"   帧率: {status_data['fps']} FPS")
                    print(f"   客户端数: {status_data['clients']}")
                    print(f"   可用内存: {status_data['free_heap']} bytes")
                    print(f"   运行时间: {status_data['uptime']/1000:.1f} 秒")
                    
            except json.JSONDecodeError:
                print_result("JSON解析", False, "响应不是有效的JSON格式")
                
    except Exception as e:
        print_result("状态API访问", False, f"连接异常: {e}")

def test_tcp_server():
    """测试TCP服务器"""
    print_header("TCP服务器测试")
    
    try:
        # 尝试连接TCP服务器
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(TEST_TIMEOUT)
        
        result = sock.connect_ex((ESP8266_IP, TCP_PORT))
        tcp_success = result == 0
        
        print_result("TCP连接", tcp_success,
                    "端口开放" if tcp_success else f"连接失败，错误码: {result}")
        
        if tcp_success:
            # 测试发送心跳包
            try:
                sock.send(b"HB")
                print_result("心跳包发送", True, "心跳包发送成功")
            except Exception as e:
                print_result("心跳包发送", False, f"发送失败: {e}")
        
        sock.close()
        
    except Exception as e:
        print_result("TCP连接", False, f"连接异常: {e}")

def test_websocket_endpoint():
    """测试WebSocket端点"""
    print_header("WebSocket端点测试")
    
    try:
        # 检查WebSocket端点是否可访问
        response = requests.get(f"http://{ESP8266_IP}:{WEB_PORT}/ws", timeout=TEST_TIMEOUT)
        # WebSocket端点通常返回400或426状态码
        ws_accessible = response.status_code in [400, 426, 200]
        print_result("WebSocket端点", ws_accessible,
                    f"状态码: {response.status_code} (WebSocket升级请求)")
        
    except Exception as e:
        print_result("WebSocket端点", False, f"访问异常: {e}")

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 ESP8266 + K230 实时图像传输系统测试")
    print(f"📍 测试目标: {ESP8266_IP}")
    print(f"⏰ 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行各项测试
    test_network_connectivity()
    test_web_server()
    test_status_api()
    test_tcp_server()
    test_websocket_endpoint()
    
    # 测试总结
    print_header("测试总结")
    print("✅ 如果所有测试都通过，系统应该可以正常工作")
    print("❌ 如果有测试失败，请检查:")
    print("   1. ESP8266是否正常启动并连接网络")
    print("   2. 网络配置是否正确")
    print("   3. 防火墙是否阻止连接")
    print("   4. 设备是否在同一网络中")
    
    print(f"\n📋 下一步:")
    print(f"   1. 确保ESP8266程序正常运行")
    print(f"   2. 运行K230程序: python k230_video_stream.py")
    print(f"   3. 访问监控页面: http://{ESP8266_IP}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        global ESP8266_IP
        ESP8266_IP = sys.argv[1]
        print(f"使用指定IP地址: {ESP8266_IP}")
    
    try:
        run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")

if __name__ == "__main__":
    main()
