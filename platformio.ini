; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp01_1m]
platform = espressif8266
board = esp01_1m
framework = arduino
board_upload.resetmethod = nodemcu
board_build.flash_mode = dout
monitor_speed = 115200
upload_speed = 256000
lib_deps = 
	esphome/ESPAsyncWebServer-esphome@^3.4.0
	esp32async/ESPAsyncTCP@^2.0.0
